"""
Event system for real-time communication between bot components.
"""

import asyncio
from typing import Any, Dict, List, Callable, Optional, Type
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import uuid

from ..utils.logger import get_logger

logger = get_logger(__name__)


class EventType(str, Enum):
    """Event types for the bot system."""
    
    # Twitch events
    TWITCH_MESSAGE = "twitch.message"
    TWITCH_SUBSCRIPTION = "twitch.subscription"
    TWITCH_DONATION = "twitch.donation"
    TWITCH_FOLLOW = "twitch.follow"
    TWITCH_RAID = "twitch.raid"
    TWITCH_HOST = "twitch.host"
    TWITCH_BITS = "twitch.bits"
    
    # AI events
    AI_RESPONSE_GENERATED = "ai.response_generated"
    AI_RESPONSE_SENT = "ai.response_sent"
    AI_ERROR = "ai.error"
    
    # Audio events
    AUDIO_TTS_REQUEST = "audio.tts_request"
    AUDIO_TTS_COMPLETE = "audio.tts_complete"
    AUDIO_STT_RESULT = "audio.stt_result"
    
    # System events
    SYSTEM_STARTUP = "system.startup"
    SYSTEM_SHUTDOWN = "system.shutdown"
    SYSTEM_ERROR = "system.error"
    CONFIG_CHANGED = "system.config_changed"
    
    # Feature events
    TRIVIA_QUESTION = "trivia.question"
    TRIVIA_ANSWER = "trivia.answer"
    GOAL_PROGRESS = "goal.progress"
    REMINDER_TRIGGERED = "reminder.triggered"
    
    # User events
    USER_COMMAND = "user.command"
    USER_JOINED = "user.joined"
    USER_LEFT = "user.left"


@dataclass
class Event:
    """Base event class for the event system."""
    
    type: EventType
    data: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    source: Optional[str] = None
    priority: int = 0  # Higher numbers = higher priority
    
    def __post_init__(self):
        """Validate event after initialization."""
        if not isinstance(self.type, EventType):
            raise ValueError(f"Event type must be an EventType, got {type(self.type)}")


EventHandler = Callable[[Event], None]
AsyncEventHandler = Callable[[Event], asyncio.Task]


class EventBus:
    """
    Central event bus for managing event distribution throughout the bot.
    """
    
    def __init__(self, max_queue_size: int = 1000):
        self.max_queue_size = max_queue_size
        self._handlers: Dict[EventType, List[EventHandler]] = {}
        self._async_handlers: Dict[EventType, List[AsyncEventHandler]] = {}
        self._event_queue: asyncio.Queue = asyncio.Queue(maxsize=max_queue_size)
        self._running = False
        self._processor_task: Optional[asyncio.Task] = None
        self._stats = {
            "events_processed": 0,
            "events_dropped": 0,
            "handler_errors": 0,
        }
    
    async def start(self) -> None:
        """Start the event bus processor."""
        if self._running:
            return
            
        self._running = True
        self._processor_task = asyncio.create_task(self._process_events())
        logger.info("Event bus started")
    
    async def stop(self) -> None:
        """Stop the event bus processor."""
        if not self._running:
            return
            
        self._running = False
        
        if self._processor_task:
            self._processor_task.cancel()
            try:
                await self._processor_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Event bus stopped")
    
    def subscribe(self, event_type: EventType, handler: EventHandler) -> None:
        """
        Subscribe a synchronous handler to an event type.
        
        Args:
            event_type: Type of event to subscribe to
            handler: Function to call when event occurs
        """
        if event_type not in self._handlers:
            self._handlers[event_type] = []
        
        self._handlers[event_type].append(handler)
        logger.debug(f"Subscribed handler to {event_type}")
    
    def subscribe_async(self, event_type: EventType, handler: AsyncEventHandler) -> None:
        """
        Subscribe an asynchronous handler to an event type.
        
        Args:
            event_type: Type of event to subscribe to
            handler: Async function to call when event occurs
        """
        if event_type not in self._async_handlers:
            self._async_handlers[event_type] = []
        
        self._async_handlers[event_type].append(handler)
        logger.debug(f"Subscribed async handler to {event_type}")
    
    def unsubscribe(self, event_type: EventType, handler: EventHandler) -> None:
        """Unsubscribe a handler from an event type."""
        if event_type in self._handlers and handler in self._handlers[event_type]:
            self._handlers[event_type].remove(handler)
            logger.debug(f"Unsubscribed handler from {event_type}")
    
    def unsubscribe_async(self, event_type: EventType, handler: AsyncEventHandler) -> None:
        """Unsubscribe an async handler from an event type."""
        if event_type in self._async_handlers and handler in self._async_handlers[event_type]:
            self._async_handlers[event_type].remove(handler)
            logger.debug(f"Unsubscribed async handler from {event_type}")
    
    async def emit(self, event: Event) -> None:
        """
        Emit an event to the event bus.
        
        Args:
            event: Event to emit
        """
        try:
            await self._event_queue.put(event)
            logger.debug(f"Emitted event: {event.type} (ID: {event.event_id})")
        except asyncio.QueueFull:
            self._stats["events_dropped"] += 1
            logger.warning(f"Event queue full, dropped event: {event.type}")
    
    async def emit_and_wait(self, event: Event) -> None:
        """
        Emit an event and wait for all handlers to complete.
        
        Args:
            event: Event to emit
        """
        await self._handle_event(event)
    
    def get_stats(self) -> Dict[str, int]:
        """Get event bus statistics."""
        return self._stats.copy()
    
    async def _process_events(self) -> None:
        """Main event processing loop."""
        while self._running:
            try:
                # Get event with timeout to allow for shutdown
                event = await asyncio.wait_for(self._event_queue.get(), timeout=1.0)
                await self._handle_event(event)
                self._stats["events_processed"] += 1
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"Error processing event: {e}")
    
    async def _handle_event(self, event: Event) -> None:
        """
        Handle a single event by calling all registered handlers.
        
        Args:
            event: Event to handle
        """
        # Handle synchronous handlers
        sync_handlers = self._handlers.get(event.type, [])
        for handler in sync_handlers:
            try:
                handler(event)
            except Exception as e:
                self._stats["handler_errors"] += 1
                logger.error(f"Error in sync event handler for {event.type}: {e}")
        
        # Handle asynchronous handlers
        async_handlers = self._async_handlers.get(event.type, [])
        if async_handlers:
            tasks = []
            for handler in async_handlers:
                try:
                    task = asyncio.create_task(handler(event))
                    tasks.append(task)
                except Exception as e:
                    self._stats["handler_errors"] += 1
                    logger.error(f"Error creating task for async handler {event.type}: {e}")
            
            # Wait for all async handlers to complete
            if tasks:
                try:
                    await asyncio.gather(*tasks, return_exceptions=True)
                except Exception as e:
                    self._stats["handler_errors"] += 1
                    logger.error(f"Error in async event handlers for {event.type}: {e}")


# Convenience functions for creating common events
def create_twitch_message_event(username: str, message: str, channel: str, **kwargs) -> Event:
    """Create a Twitch message event."""
    return Event(
        type=EventType.TWITCH_MESSAGE,
        data={
            "username": username,
            "message": message,
            "channel": channel,
            **kwargs
        },
        source="twitch"
    )


def create_ai_response_event(response: str, context: Dict[str, Any], **kwargs) -> Event:
    """Create an AI response event."""
    return Event(
        type=EventType.AI_RESPONSE_GENERATED,
        data={
            "response": response,
            "context": context,
            **kwargs
        },
        source="ai"
    )


def create_system_event(event_type: EventType, message: str, **kwargs) -> Event:
    """Create a system event."""
    return Event(
        type=event_type,
        data={
            "message": message,
            **kwargs
        },
        source="system"
    )
