# Harmony: AI Twitch Co-Host Bo<PERSON>

[![<PERSON>ra Ticket](https://img.shields.io/badge/Jira-SMS--7-blue)](https://your-jira-instance.atlassian.net/browse/SMS-7)
[![Python](https://img.shields.io/badge/Python-3.9+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

Harmony is an intelligent, engaging, and customizable AI companion for Twitch streamers, designed to enhance viewer interaction and provide valuable assistance during live streams.

## 🌟 Features

### Core Interaction & Personality
- **Natural Language Understanding**: Advanced NLU with sentiment analysis and context awareness
- **Conversational Memory**: Maintains context within sessions and remembers recurring viewers
- **Customizable Personality**: Configurable traits, speaking style, and response patterns
- **Dynamic Reactivity**: Real-time adaptation to stream mood and events
- **Expressive Voice Synthesis**: High-quality TTS with emotional expression

### Twitch Integration
- **Real-time Chat Processing**: Seamless chat monitoring and response
- **OAuth Authentication**: Secure login for both streamer and bot accounts
- **Alert Recognition**: Responds to subs, donations, raids, follows, and more
- **Command Handling**: Custom commands and macros support
- **Stream Metadata Awareness**: Monitors game, title, viewer count, uptime

### AI & Voice
- **Google Gemini Integration**: Primary LLM with advanced conversation capabilities
- **ElevenLabs TTS**: Premium voice synthesis with emotional expression
- **Ollama Fallback**: Local model support for offline operation
- **Perplexity Research**: Enhanced knowledge and real-time information

### Entertainment & Utility
- **Interactive Trivia**: Host engaging quiz segments with scorekeeping
- **FAQ Management**: Configurable knowledge base for common questions
- **Goal Tracking**: Monitor and announce stream goals and milestones
- **Basic Moderation**: Intelligent chat monitoring with configurable rules
- **Reminder System**: Scheduled announcements and notifications

### Technical Features
- **Desktop GUI**: User-friendly interface for configuration and monitoring
- **Event-Driven Architecture**: Real-time processing with low latency
- **Secure Credential Storage**: Encrypted token management
- **Comprehensive Logging**: Detailed activity tracking and debugging
- **Plugin System**: Modular feature management with toggles

## 🚀 Quick Start

### Prerequisites
- Python 3.9 or higher
- Google Gemini API key
- ElevenLabs API key
- Twitch application credentials

### Installation

#### Option 1: Enhanced Setup (Recommended)
The enhanced setup script creates a virtual environment, installs dependencies, and guides you through API key configuration:

```bash
# Clone the repository
git clone https://github.com/your-username/harmony-bot.git
cd harmony-bot

# Run the enhanced setup script
python setup.py
```

This will:
- ✅ Create a virtual environment
- ✅ Install all dependencies (core + optional)
- ✅ Guide you through API key configuration
- ✅ Create necessary directories
- ✅ Verify the installation

#### Option 2: Core Dependencies Only
For minimal installation with just essential features:

```bash
# Clone the repository
git clone https://github.com/your-username/harmony-bot.git
cd harmony-bot

# Create virtual environment
python -m venv .venv

# Activate virtual environment
# Windows:
.venv\Scripts\activate
# Unix/Linux/macOS:
source .venv/bin/activate

# Install core dependencies only
pip install -r requirements-core.txt

# Copy and edit environment file
cp .env.example .env
# Edit .env with your API keys
```

#### Option 3: Manual Installation
1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/harmony-bot.git
   cd harmony-bot
   ```

2. **Create virtual environment**
   ```bash
   python -m venv .venv
   ```

3. **Activate virtual environment**
   ```bash
   # Windows:
   .venv\Scripts\activate
   # Unix/Linux/macOS:
   source .venv/bin/activate
   ```

4. **Install dependencies**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

5. **Configure environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and credentials
   ```

6. **Create necessary directories**
   ```bash
   mkdir -p data/logs data/audio cache
   ```

#### Quick Start After Installation

```bash
# Activate virtual environment (if not already active)
# Windows:
activate.bat
# Unix/Linux/macOS:
source activate.sh

# Test the installation
python run.py --help

# Start the bot
python run.py
```

### Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
# Google Gemini API
GOOGLE_API_KEY=your_gemini_api_key_here

# ElevenLabs TTS
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# Twitch Application
TWITCH_CLIENT_ID=your_twitch_client_id
TWITCH_CLIENT_SECRET=your_twitch_client_secret
TWITCH_REDIRECT_URI=http://localhost:8080/auth/callback

# Optional: Perplexity API
PERPLEXITY_API_KEY=your_perplexity_api_key

# Optional: Ollama (for local models)
OLLAMA_BASE_URL=http://localhost:11434
```

## 🏗️ Architecture

### Project Structure
```
harmony_bot/
├── src/harmony/              # Main package
│   ├── core/                # Bot orchestration and events
│   ├── twitch/              # Twitch integration and OAuth
│   ├── ai/                  # LLM integration and conversation
│   ├── audio/               # TTS processing
│   ├── gui/                 # Desktop interface
│   ├── utils/               # Security, logging, helpers
│   ├── config/              # Configuration management
│   ├── database/            # Data persistence
│   └── features/            # Trivia, moderation, goals, etc.
├── tests/                   # Comprehensive unit tests
├── config/                  # YAML configuration files
├── docs/                    # Documentation
└── requirements.txt         # Dependencies
```

### Key Components
- **Bot Core**: Main orchestrator managing all subsystems
- **Event System**: Pub/sub architecture for real-time communication
- **Twitch Client**: Chat monitoring, API integration, OAuth handling
- **AI Engine**: LLM integration with personality and memory management
- **Audio Processor**: TTS with voice synthesis and audio streaming
- **Configuration Manager**: Type-safe settings with hot-reloading
- **Security Manager**: Encrypted credential storage and session management

## 🎮 Usage

### First-Time Setup

1. **Launch the GUI**
   ```bash
   python -m harmony.gui.main
   ```

2. **Configure Twitch Authentication**
   - Click "Setup Twitch" in the GUI
   - Follow OAuth flow for both streamer and bot accounts
   - Test connection to ensure proper setup

3. **Customize Personality**
   - Navigate to "Personality" tab
   - Choose from presets or create custom personality
   - Adjust voice settings and response patterns

4. **Enable Features**
   - Use feature toggles to enable desired functionality
   - Configure trivia questions, moderation rules, etc.
   - Set up goal tracking and reminder schedules

### Command Line Interface

```bash
# Start with specific configuration
python run.py --config config/streaming.yaml

# Enable debug mode
python run.py --debug

# Run specific features only
python run.py --features chat,tts,trivia
```

## 🔧 Configuration

### Personality Presets

Harmony comes with several built-in personality presets:

- **Cheerful Companion**: Upbeat, encouraging, family-friendly
- **Sassy Sidekick**: Witty, playful, with gentle teasing
- **Analytical Assistant**: Informative, helpful, detail-oriented
- **Calm Curator**: Relaxed, thoughtful, meditative tone

### Custom Personality

Create custom personalities by editing `config/personality_presets.yaml`:

```yaml
custom_personality:
  name: "My Custom Bot"
  traits:
    - energetic
    - helpful
    - humorous
  speaking_style:
    formality: casual
    verbosity: moderate
    emoji_usage: frequent
  voice_settings:
    stability: 0.7
    similarity_boost: 0.8
    style: 0.6
```

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
pytest

# Run specific test categories
pytest tests/test_twitch/
pytest tests/test_ai/

# Run with coverage
pytest --cov=harmony --cov-report=html
```

## 📚 Documentation

- [Setup Guide](docs/setup.md) - Detailed installation and configuration
- [Configuration Reference](docs/configuration.md) - Complete settings documentation
- [API Reference](docs/api_reference.md) - Developer documentation
- [Troubleshooting](docs/troubleshooting.md) - Common issues and solutions

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Google Gemini](https://ai.google.dev/gemini-api/docs) for advanced AI capabilities
- [ElevenLabs](https://elevenlabs.io) for premium voice synthesis
- [TwitchIO](https://github.com/TwitchIO/TwitchIO) for Twitch integration
- [Ollama](https://ollama.ai) for local model support

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/your-username/harmony-bot/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/harmony-bot/discussions)
- **Jira**: [Project Harmony](https://your-jira-instance.atlassian.net/browse/SMS-7)

---

**Co-authored by [Augment Code](https://www.augmentcode.com/?utm_source=github&utm_medium=readme&utm_campaign=harmony_bot)**
